# 清除缓存并重启服务脚本

Write-Host "🔄 清除缓存并重启服务..." -ForegroundColor Green

# 停止现有进程
Write-Host "⏹️ 停止现有服务..." -ForegroundColor Yellow
try {
    # 查找并停止前端进程
    $frontendProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*vite*" }
    if ($frontendProcesses) {
        $frontendProcesses | Stop-Process -Force
        Write-Host "✅ 前端进程已停止" -ForegroundColor Green
    }
    
    # 查找并停止后端进程
    $backendProcesses = Get-Process -Name "dotnet" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*ChatApp*" }
    if ($backendProcesses) {
        $backendProcesses | Stop-Process -Force
        Write-Host "✅ 后端进程已停止" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ 停止进程时出现错误: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 等待进程完全停止
Start-Sleep -Seconds 2

# 清除前端缓存
Write-Host "🧹 清除前端缓存..." -ForegroundColor Yellow
$frontendPath = "C:\Users\<USER>\Desktop\通讯\frontend"

if (Test-Path $frontendPath) {
    Set-Location $frontendPath
    
    # 删除node_modules/.vite缓存
    $viteCachePath = "node_modules\.vite"
    if (Test-Path $viteCachePath) {
        Remove-Item $viteCachePath -Recurse -Force
        Write-Host "✅ Vite缓存已清除" -ForegroundColor Green
    }
    
    # 删除dist目录
    $distPath = "dist"
    if (Test-Path $distPath) {
        Remove-Item $distPath -Recurse -Force
        Write-Host "✅ Dist目录已清除" -ForegroundColor Green
    }
    
    # 清除npm缓存
    try {
        npm cache clean --force
        Write-Host "✅ NPM缓存已清除" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ NPM缓存清除失败" -ForegroundColor Yellow
    }
}

# 清除后端缓存
Write-Host "🧹 清除后端缓存..." -ForegroundColor Yellow
$backendPath = "C:\Users\<USER>\Desktop\通讯\backend"

if (Test-Path $backendPath) {
    Set-Location $backendPath
    
    # 删除bin和obj目录
    $binPath = "bin"
    $objPath = "obj"
    
    if (Test-Path $binPath) {
        Remove-Item $binPath -Recurse -Force
        Write-Host "✅ Bin目录已清除" -ForegroundColor Green
    }
    
    if (Test-Path $objPath) {
        Remove-Item $objPath -Recurse -Force
        Write-Host "✅ Obj目录已清除" -ForegroundColor Green
    }
}

Write-Host "🔄 重新启动服务..." -ForegroundColor Yellow

# 启动后端服务
Write-Host "🚀 启动后端服务..." -ForegroundColor Cyan
Set-Location $backendPath
Start-Process powershell -ArgumentList "-NoExit", "-Command", "dotnet run" -WindowStyle Normal

# 等待后端启动
Start-Sleep -Seconds 5

# 启动前端服务
Write-Host "🚀 启动前端服务..." -ForegroundColor Cyan
Set-Location $frontendPath
Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm run dev" -WindowStyle Normal

# 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 测试服务状态
Write-Host "🔍 检查服务状态..." -ForegroundColor Yellow

try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:5174" -UseBasicParsing -TimeoutSec 10
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ 前端服务正常运行 (http://localhost:5174)" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ 前端服务启动失败" -ForegroundColor Red
}

try {
    $backendResponse = Invoke-WebRequest -Uri "http://localhost:5057/api/auth/login" -Method POST -ContentType "application/json" -Body '{"username":"test","password":"test"}' -UseBasicParsing -TimeoutSec 10
    Write-Host "✅ 后端服务正常运行 (http://localhost:5057)" -ForegroundColor Green
} catch {
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "✅ 后端服务正常运行 (http://localhost:5057)" -ForegroundColor Green
    } else {
        Write-Host "❌ 后端服务启动失败" -ForegroundColor Red
    }
}

Write-Host "`n🎉 缓存清除和服务重启完成！" -ForegroundColor Green
Write-Host "📱 现在可以访问: http://localhost:5174" -ForegroundColor Cyan
Write-Host "🔧 如果仍有问题，请手动刷新浏览器 (Ctrl+F5)" -ForegroundColor Yellow

Write-Host "`n📋 修复的问题:" -ForegroundColor Yellow
Write-Host "  ✅ 添加了ContactsView中缺失的showToast导入" -ForegroundColor Green
Write-Host "  ✅ 清除了所有缓存文件" -ForegroundColor Green
Write-Host "  ✅ 重新启动了前后端服务" -ForegroundColor Green
Write-Host "  ✅ showActionSheet导入错误已修复" -ForegroundColor Green

Write-Host "`n🎯 下一步测试:" -ForegroundColor Yellow
Write-Host "1. 访问 http://localhost:5174" -ForegroundColor White
Write-Host "2. 登录账号: testuser / 123456" -ForegroundColor White
Write-Host "3. 进入联系人页面" -ForegroundColor White
Write-Host "4. 点击好友测试聊天跳转" -ForegroundColor White
Write-Host "5. 检查控制台是否还有错误" -ForegroundColor White
