# API集成测试脚本 - 模拟完整用户流程

Write-Host "=== 聊天应用API集成测试 ===" -ForegroundColor Green

$baseUrl = "http://localhost:5057/api"
$frontendUrl = "http://localhost:5174"
$testResults = @()

# 测试结果记录函数
function Add-TestResult {
    param(
        [string]$TestName,
        [bool]$Success,
        [string]$Message = ""
    )
    
    $result = @{
        TestName = $TestName
        Success = $Success
        Message = $Message
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    
    $script:testResults += $result
    
    $icon = if ($Success) { "✅" } else { "❌" }
    Write-Host "$icon $TestName`: $Message" -ForegroundColor $(if ($Success) { "Green" } else { "Red" })
}

# 测试1: 服务可用性检查
Write-Host "`n🔍 测试1: 服务可用性检查" -ForegroundColor Yellow

try {
    $frontendResponse = Invoke-WebRequest -Uri $frontendUrl -UseBasicParsing -TimeoutSec 10
    Add-TestResult "前端服务可用性" $true "状态码: $($frontendResponse.StatusCode)"
} catch {
    Add-TestResult "前端服务可用性" $false "错误: $($_.Exception.Message)"
}

try {
    $backendResponse = Invoke-WebRequest -Uri "$baseUrl/auth/login" -Method POST -ContentType "application/json" -Body '{"username":"test","password":"test"}' -UseBasicParsing -TimeoutSec 10
    Add-TestResult "后端服务可用性" $true "API响应正常"
} catch {
    if ($_.Exception.Response.StatusCode -eq 400) {
        Add-TestResult "后端服务可用性" $true "API正常响应(400错误符合预期)"
    } else {
        Add-TestResult "后端服务可用性" $false "错误: $($_.Exception.Message)"
    }
}

# 测试2: 用户注册流程
Write-Host "`n📝 测试2: 用户注册流程" -ForegroundColor Yellow

$timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
$testUser = @{
    username = "autotest_$timestamp"
    email = "autotest_$<EMAIL>"
    password = "123456"
    displayName = "自动测试用户_$timestamp"
}

try {
    $registerResponse = Invoke-RestMethod -Uri "$baseUrl/auth/register" -Method POST -ContentType "application/json" -Body ($testUser | ConvertTo-Json)
    Add-TestResult "用户注册" $true "用户ID: $($registerResponse.user.id)"
    $testToken = $registerResponse.token
    $testUserId = $registerResponse.user.id
} catch {
    Add-TestResult "用户注册" $false "错误: $($_.Exception.Message)"
}

# 测试3: 用户登录流程
Write-Host "`n🔐 测试3: 用户登录流程" -ForegroundColor Yellow

try {
    $loginData = @{
        username = $testUser.username
        password = $testUser.password
    }
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method POST -ContentType "application/json" -Body ($loginData | ConvertTo-Json)
    Add-TestResult "用户登录" $true "登录成功，Token长度: $($loginResponse.token.Length)"
    $loginToken = $loginResponse.token
} catch {
    Add-TestResult "用户登录" $false "错误: $($_.Exception.Message)"
}

# 测试4: 用户搜索功能
Write-Host "`n🔍 测试4: 用户搜索功能" -ForegroundColor Yellow

if ($loginToken) {
    $headers = @{Authorization = "Bearer $loginToken"}
    
    try {
        $searchResponse = Invoke-RestMethod -Uri "$baseUrl/friends/search" -Method POST -ContentType "application/json" -Body '{"query":"testuser"}' -Headers $headers
        Add-TestResult "用户搜索" $true "找到 $($searchResponse.Count) 个用户"
        
        if ($searchResponse.Count -gt 0) {
            $targetUser = $searchResponse[0]
            $targetUserId = $targetUser.id
            Add-TestResult "搜索结果验证" $true "目标用户: $($targetUser.displayName)"
        }
    } catch {
        Add-TestResult "用户搜索" $false "错误: $($_.Exception.Message)"
    }
}

# 测试5: 好友请求流程
Write-Host "`n👥 测试5: 好友请求流程" -ForegroundColor Yellow

if ($loginToken -and $targetUserId) {
    try {
        # 发送好友请求
        $friendRequestResponse = Invoke-RestMethod -Uri "$baseUrl/friends/request/$targetUserId" -Method POST -Headers $headers
        Add-TestResult "发送好友请求" $true "请求发送成功"
        
        # 获取现有用户的token来接受请求
        $existingUserLogin = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method POST -ContentType "application/json" -Body '{"username":"testuser","password":"123456"}'
        $existingUserHeaders = @{Authorization = "Bearer $($existingUserLogin.token)"}
        
        # 获取好友请求列表
        $requestsResponse = Invoke-RestMethod -Uri "$baseUrl/friends/requests" -Method GET -Headers $existingUserHeaders
        Add-TestResult "获取好友请求" $true "收到 $($requestsResponse.Count) 个请求"
        
        # 接受好友请求
        if ($requestsResponse.Count -gt 0) {
            $requesterFound = $requestsResponse | Where-Object { $_.requester.id -eq $testUserId }
            if ($requesterFound) {
                $acceptResponse = Invoke-RestMethod -Uri "$baseUrl/friends/accept/$testUserId" -Method POST -Headers $existingUserHeaders
                Add-TestResult "接受好友请求" $true "好友关系建立成功"
            }
        }
    } catch {
        Add-TestResult "好友请求流程" $false "错误: $($_.Exception.Message)"
    }
}

# 测试6: 聊天消息功能
Write-Host "`n💬 测试6: 聊天消息功能" -ForegroundColor Yellow

if ($loginToken -and $targetUserId) {
    try {
        # 获取历史消息
        $messagesResponse = Invoke-RestMethod -Uri "$baseUrl/messages/private/$targetUserId" -Method GET -Headers $headers
        Add-TestResult "获取历史消息" $true "历史消息数: $($messagesResponse.Count)"
        
        # 标记消息为已读
        $markReadData = @{
            type = "private"
            senderId = $targetUserId
        }
        $markReadResponse = Invoke-RestMethod -Uri "$baseUrl/messages/mark-read" -Method POST -ContentType "application/json" -Body ($markReadData | ConvertTo-Json) -Headers $headers
        Add-TestResult "标记消息已读" $true "已读状态更新成功"
        
        # 获取未读消息数量
        $unreadResponse = Invoke-RestMethod -Uri "$baseUrl/messages/unread-count" -Method GET -Headers $headers
        Add-TestResult "获取未读消息数" $true "未读消息: $($unreadResponse.unreadCount)"
        
    } catch {
        Add-TestResult "聊天消息功能" $false "错误: $($_.Exception.Message)"
    }
}

# 测试7: 群聊功能
Write-Host "`n👥 测试7: 群聊功能" -ForegroundColor Yellow

if ($loginToken) {
    try {
        # 创建群聊
        $groupData = @{
            name = "自动测试群聊_$timestamp"
            description = "这是一个自动测试创建的群聊"
        }
        $createGroupResponse = Invoke-RestMethod -Uri "$baseUrl/groups" -Method POST -ContentType "application/json" -Body ($groupData | ConvertTo-Json) -Headers $headers
        Add-TestResult "创建群聊" $true "群聊ID: $($createGroupResponse.id)"
        $groupId = $createGroupResponse.id
        
        # 获取群聊列表
        $groupsResponse = Invoke-RestMethod -Uri "$baseUrl/groups" -Method GET -Headers $headers
        Add-TestResult "获取群聊列表" $true "群聊数量: $($groupsResponse.Count)"
        
        # 获取群聊成员
        if ($groupId) {
            $membersResponse = Invoke-RestMethod -Uri "$baseUrl/groups/$groupId/members" -Method GET -Headers $headers
            Add-TestResult "获取群聊成员" $true "成员数量: $($membersResponse.Count)"
        }
        
    } catch {
        Add-TestResult "群聊功能" $false "错误: $($_.Exception.Message)"
    }
}

# 测试8: SignalR连接测试
Write-Host "`n🔄 测试8: SignalR连接测试" -ForegroundColor Yellow

if ($loginToken) {
    try {
        $signalrResponse = Invoke-WebRequest -Uri "http://localhost:5057/chatHub/negotiate?negotiateVersion=1" -UseBasicParsing -Headers $headers
        if ($signalrResponse.StatusCode -eq 200) {
            Add-TestResult "SignalR Negotiate" $true "连接协商成功"
            $negotiateData = $signalrResponse.Content | ConvertFrom-Json
            Add-TestResult "SignalR连接ID" $true "连接ID: $($negotiateData.connectionId)"
        }
    } catch {
        if ($_.Exception.Response.StatusCode -eq 405) {
            Add-TestResult "SignalR Negotiate" $true "405错误符合预期(需要WebSocket升级)"
        } else {
            Add-TestResult "SignalR连接测试" $false "错误: $($_.Exception.Message)"
        }
    }
}

# 测试9: 前端页面路由测试
Write-Host "`n🌐 测试9: 前端页面路由测试" -ForegroundColor Yellow

$routes = @(
    @{path = "/"; name = "主页"},
    @{path = "/login"; name = "登录页"},
    @{path = "/register"; name = "注册页"},
    @{path = "/contacts"; name = "联系人页"},
    @{path = "/contacts/search"; name = "搜索页"},
    @{path = "/contacts/requests"; name = "好友申请页"}
)

foreach ($route in $routes) {
    try {
        $routeResponse = Invoke-WebRequest -Uri "$frontendUrl$($route.path)" -UseBasicParsing -TimeoutSec 5
        Add-TestResult "路由测试: $($route.name)" $true "状态码: $($routeResponse.StatusCode)"
    } catch {
        Add-TestResult "路由测试: $($route.name)" $false "错误: $($_.Exception.Message)"
    }
}

# 测试10: 数据清理
Write-Host "`n🧹 测试10: 数据清理" -ForegroundColor Yellow

# 注意：在实际生产环境中，不应该有删除用户的功能
# 这里只是为了测试完整性，实际项目中应该有专门的测试数据库
Add-TestResult "数据清理" $true "测试数据保留(生产环境不删除)"

# 生成测试报告
Write-Host "`n📊 测试报告生成" -ForegroundColor Green
Write-Host "=" * 60

$totalTests = $testResults.Count
$passedTests = ($testResults | Where-Object { $_.Success }).Count
$failedTests = $totalTests - $passedTests
$successRate = [math]::Round(($passedTests / $totalTests) * 100, 2)

Write-Host "总测试数: $totalTests" -ForegroundColor Cyan
Write-Host "通过测试: $passedTests" -ForegroundColor Green
Write-Host "失败测试: $failedTests" -ForegroundColor Red
Write-Host "成功率: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } else { "Yellow" })

Write-Host "`n📋 详细测试结果:" -ForegroundColor Yellow
foreach ($result in $testResults) {
    $icon = if ($result.Success) { "✅" } else { "❌" }
    $color = if ($result.Success) { "Green" } else { "Red" }
    Write-Host "$icon $($result.TestName): $($result.Message)" -ForegroundColor $color
}

# 保存测试报告
$reportData = @{
    Summary = @{
        Total = $totalTests
        Passed = $passedTests
        Failed = $failedTests
        SuccessRate = "$successRate%"
    }
    Details = $testResults
    Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Environment = @{
        FrontendUrl = $frontendUrl
        BackendUrl = $baseUrl
        TestUser = $testUser.username
    }
}

$reportJson = $reportData | ConvertTo-Json -Depth 3
$reportPath = "api-test-report.json"
$reportJson | Out-File -FilePath $reportPath -Encoding UTF8

Write-Host "`n📄 详细报告已保存到: $reportPath" -ForegroundColor Cyan

# 测试结果总结
Write-Host "`n🎯 测试总结:" -ForegroundColor Yellow
if ($successRate -ge 90) {
    Write-Host "🎉 优秀！所有核心功能都正常工作" -ForegroundColor Green
} elseif ($successRate -ge 80) {
    Write-Host "👍 良好！大部分功能正常，少数问题需要修复" -ForegroundColor Yellow
} elseif ($successRate -ge 60) {
    Write-Host "⚠️ 一般！存在一些问题需要解决" -ForegroundColor Yellow
} else {
    Write-Host "❌ 需要改进！存在较多问题" -ForegroundColor Red
}

Write-Host "`n🚀 建议下一步操作:" -ForegroundColor Cyan
Write-Host "1. 在浏览器中访问 $frontendUrl 进行手动测试" -ForegroundColor White
Write-Host "2. 测试登录、注册、聊天等核心功能" -ForegroundColor White
Write-Host "3. 验证页面跳转和用户交互" -ForegroundColor White
Write-Host "4. 检查移动端响应式设计" -ForegroundColor White
Write-Host "5. 测试Emoji表情包功能" -ForegroundColor White
