<template>
  <div class="chat-container">
    <van-nav-bar
      :title="currentChat?.name || '聊天'"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
    >
      <template #right>
        <van-icon name="ellipsis" size="18" @click="showChatMenu = true" />
      </template>
    </van-nav-bar>

    <!-- 消息列表 -->
    <div class="message-list" ref="messageListRef">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <div class="messages">
          <div
            v-for="message in currentMessages"
            :key="message.id"
            :class="['message-item', { 'own-message': message.senderId === currentUserId }]"
          >
            <div class="message-avatar">
              <van-image
                v-if="message.senderId !== currentUserId"
                :src="getMessageAvatar(message)"
                round
                width="32"
                height="32"
                fit="cover"
              />
            </div>
            
            <div class="message-content">
              <div v-if="message.senderId !== currentUserId" class="message-sender">
                {{ message.senderName }}
              </div>
              
              <div class="message-bubble">
                <div class="message-text">{{ message.content }}</div>
                <div class="message-time">
                  {{ formatMessageTime(message.createdAt) }}
                </div>
              </div>
            </div>
            
            <div class="message-avatar">
              <van-image
                v-if="message.senderId === currentUserId"
                :src="currentUserAvatar"
                round
                width="32"
                height="32"
                fit="cover"
              />
            </div>
          </div>
        </div>
      </van-pull-refresh>
    </div>

    <!-- 输入框 -->
    <div class="input-area">
      <div class="input-toolbar">
        <div class="input-actions">
          <van-button
            icon="smile-o"
            size="small"
            plain
            @click="toggleEmojiPicker"
            class="emoji-button"
            :class="{ active: showEmojiPicker }"
          />
          <van-button
            icon="plus"
            size="small"
            plain
            @click="showMoreActions"
            class="more-button"
          />
        </div>

        <van-field
          v-model="inputMessage"
          placeholder="输入消息..."
          type="textarea"
          autosize
          maxlength="1000"
          @keyup.enter="sendMessage"
          @focus="hideEmojiPicker"
          class="message-input"
          ref="messageInputRef"
        />

        <van-button
          size="small"
          type="primary"
          :disabled="!inputMessage.trim()"
          @click="sendMessage"
          :loading="sending"
          class="send-button"
        >
          发送
        </van-button>
      </div>

      <!-- Emoji选择器 -->
      <EmojiPicker
        :visible="showEmojiPicker"
        @select="insertEmoji"
        @close="hideEmojiPicker"
      />
    </div>

    <!-- 聊天菜单 -->
    <van-action-sheet
      v-model:show="showChatMenu"
      :actions="chatMenuActions"
      @select="onChatMenuSelect"
      cancel-text="取消"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showActionSheet } from 'vant'
import { useChatStore } from '@/stores/chat'
import { useAuthStore } from '@/stores/auth'
import EmojiPicker from '@/components/EmojiPicker.vue'

const route = useRoute()
const router = useRouter()
const chatStore = useChatStore()
const authStore = useAuthStore()

const messageListRef = ref<HTMLElement>()
const messageInputRef = ref<HTMLElement>()
const inputMessage = ref('')
const refreshing = ref(false)
const sending = ref(false)
const showChatMenu = ref(false)
const showEmojiPicker = ref(false)

const chatId = computed(() => route.params.id as string)
const chatType = computed(() => route.query.type as string || 'private')
const currentChat = computed(() => chatStore.currentChat)
const currentMessages = computed(() => chatStore.currentMessages)
const currentUserId = computed(() => authStore.user?.id)
const currentUserAvatar = computed(() => authStore.user?.avatar || 'https://img.yzcdn.cn/vant/cat.jpeg')

const chatMenuActions = [
  { name: '清空聊天记录', icon: 'delete-o' },
  { name: '聊天信息', icon: 'info-o' }
]

// 获取消息发送者头像
const getMessageAvatar = (message: any) => {
  // 这里可以根据发送者ID获取头像，暂时使用默认头像
  return 'https://img.yzcdn.cn/vant/cat.jpeg'
}

// 格式化消息时间
const formatMessageTime = (timeStr: string) => {
  const time = new Date(timeStr)
  return time.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: false 
  })
}

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return
  
  const content = inputMessage.value.trim()
  inputMessage.value = ''
  sending.value = true
  
  try {
    if (currentChat.value?.type === 'private') {
      const receiverId = parseInt(chatId.value)
      await chatStore.sendPrivateMessage(receiverId, content)
    } else if (currentChat.value?.type === 'group') {
      const groupId = parseInt(chatId.value)
      await chatStore.sendGroupMessage(groupId, content)
    }
    
    // 滚动到底部
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('发送消息失败:', error)
    showToast.fail('发送失败')
    inputMessage.value = content // 恢复输入内容
  } finally {
    sending.value = false
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

// 刷新消息
const onRefresh = async () => {
  // TODO: 加载更多历史消息
  refreshing.value = false
}

// 返回
const goBack = () => {
  router.back()
}

// 聊天菜单选择
const onChatMenuSelect = (action: any) => {
  showChatMenu.value = false

  if (action.name === '清空聊天记录') {
    // TODO: 实现清空聊天记录
    console.log('清空聊天记录')
  } else if (action.name === '聊天信息') {
    // TODO: 显示聊天信息
    console.log('聊天信息')
  }
}

// Emoji相关方法
const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value
}

const hideEmojiPicker = () => {
  showEmojiPicker.value = false
}

const insertEmoji = (emoji: string) => {
  inputMessage.value += emoji
  // 聚焦到输入框
  nextTick(() => {
    if (messageInputRef.value) {
      const input = messageInputRef.value.querySelector('textarea') as HTMLTextAreaElement
      if (input) {
        input.focus()
        // 将光标移到末尾
        input.setSelectionRange(input.value.length, input.value.length)
      }
    }
  })
}

const showMoreActions = () => {
  showActionSheet({
    title: '更多功能',
    actions: [
      { name: '发送图片', icon: 'photo-o' },
      { name: '发送文件', icon: 'description' },
      { name: '发送位置', icon: 'location-o' },
      { name: '语音通话', icon: 'phone-o' },
      { name: '视频通话', icon: 'video-o' }
    ],
    onSelect: (action) => {
      showToast(`${action.name} 功能开发中...`)
    }
  })
}

// 监听消息变化，自动滚动到底部
watch(currentMessages, async () => {
  await nextTick()
  scrollToBottom()
}, { deep: true })

onMounted(async () => {
  console.log('ChatView mounted')
  console.log('Route params:', route.params)
  console.log('Route query:', route.query)
  console.log('Chat ID:', chatId.value)
  console.log('Chat Type:', chatType.value)

  // 设置当前聊天
  chatStore.setCurrentChat(chatId.value)

  // 加载历史消息
  if (chatType.value === 'private') {
    console.log('Loading private chat history')
    await chatStore.loadHistoryMessages(chatId.value, 'private')
    await chatStore.markAsRead(chatId.value, 'private')
  } else if (chatType.value === 'group') {
    console.log('Loading group chat history')
    await chatStore.loadHistoryMessages(chatId.value, 'group')
    await chatStore.markAsRead(chatId.value, 'group')
  }

  console.log('Current chat:', currentChat.value)
  console.log('Current messages:', currentMessages.value)

  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
})
</script>

<style scoped>
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--theme-background);
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 80px;
}

.messages {
  min-height: 100%;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-end;
}

.message-item.own-message {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  margin: 0 8px;
}

.message-content {
  max-width: 70%;
}

.message-sender {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  padding: 0 12px;
}

.own-message .message-sender {
  text-align: right;
}

.message-bubble {
  background: var(--theme-chat-bubble-other);
  border-radius: 18px;
  padding: 12px 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  max-width: 100%;
  word-wrap: break-word;
  font-size: 16px;
  line-height: 1.4;
  border: 1px solid var(--theme-border);

  /* 微信风格的消息气泡尾巴 */
  &::before {
    content: '';
    position: absolute;
    top: 12px;
    left: -6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-right-color: var(--theme-chat-bubble-other);
  }
}

.own-message .message-bubble {
  background: var(--theme-chat-bubble-self);
  color: #000;
  border: none;

  /* 自己的消息气泡尾巴（右侧） */
  &::before {
    right: -6px;
    left: auto;
    border-left-color: var(--theme-chat-bubble-self);
    border-right-color: transparent;
  }
}

.message-text {
  word-wrap: break-word;
  line-height: 1.4;
}

.message-time {
  font-size: 10px;
  opacity: 0.7;
  margin-top: 4px;
  text-align: right;
}

.input-area {
  background: var(--theme-surface);
  border-top: 1px solid var(--theme-border);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.input-toolbar {
  display: flex;
  align-items: flex-end;
  padding: 12px;
  gap: 8px;
}

.input-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.emoji-button,
.more-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  border: 1px solid var(--theme-border);
}

.emoji-button.active {
  background: var(--theme-primary);
  color: white;
  border-color: var(--theme-primary);
}

.emoji-button:hover,
.more-button:hover {
  background: var(--theme-border);
}

.message-input {
  flex: 1;
  background: var(--theme-background);
  border-radius: 20px;
  min-height: 36px;
  max-height: 120px;
}

.message-input :deep(.van-field__control) {
  background: transparent;
  border-radius: 20px;
  padding: 8px 16px;
  line-height: 1.4;
}

.send-button {
  min-width: 60px;
  height: 36px;
  border-radius: 18px;
  background: var(--theme-primary);
  border: none;
  font-weight: 500;
  color: white;
}

@media (max-width: 480px) {
  .message-list {
    padding: 12px;
  }
  
  .message-content {
    max-width: 75%;
  }
}
</style>
