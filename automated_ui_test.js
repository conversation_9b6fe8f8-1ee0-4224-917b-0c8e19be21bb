// 全自动UI测试脚本 - 模拟用户真实操作
// 使用Playwright进行自动化测试

const { chromium } = require('playwright');

class ChatAppTester {
  constructor() {
    this.browser = null;
    this.context = null;
    this.page = null;
    this.baseUrl = 'http://localhost:5174';
    this.testResults = [];
  }

  // 初始化浏览器
  async init() {
    console.log('🚀 启动自动化测试...');
    this.browser = await chromium.launch({ 
      headless: false, // 显示浏览器窗口
      slowMo: 1000 // 每个操作间隔1秒
    });
    this.context = await this.browser.newContext({
      viewport: { width: 375, height: 667 } // 模拟手机屏幕
    });
    this.page = await this.context.newPage();
    
    // 监听控制台错误
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.error('❌ 控制台错误:', msg.text());
      }
    });
  }

  // 记录测试结果
  logResult(testName, success, message = '') {
    const result = {
      test: testName,
      success,
      message,
      timestamp: new Date().toISOString()
    };
    this.testResults.push(result);
    
    const icon = success ? '✅' : '❌';
    console.log(`${icon} ${testName}: ${message}`);
  }

  // 等待元素并点击
  async clickElement(selector, testName) {
    try {
      await this.page.waitForSelector(selector, { timeout: 10000 });
      await this.page.click(selector);
      this.logResult(testName, true, '点击成功');
      await this.page.waitForTimeout(1000); // 等待页面响应
      return true;
    } catch (error) {
      this.logResult(testName, false, `点击失败: ${error.message}`);
      return false;
    }
  }

  // 输入文本
  async typeText(selector, text, testName) {
    try {
      await this.page.waitForSelector(selector, { timeout: 10000 });
      await this.page.fill(selector, text);
      this.logResult(testName, true, `输入成功: ${text}`);
      await this.page.waitForTimeout(500);
      return true;
    } catch (error) {
      this.logResult(testName, false, `输入失败: ${error.message}`);
      return false;
    }
  }

  // 等待页面跳转
  async waitForNavigation(expectedUrl, testName) {
    try {
      await this.page.waitForURL(expectedUrl, { timeout: 10000 });
      this.logResult(testName, true, `页面跳转成功: ${expectedUrl}`);
      return true;
    } catch (error) {
      this.logResult(testName, false, `页面跳转失败: ${error.message}`);
      return false;
    }
  }

  // 检查元素是否存在
  async checkElement(selector, testName) {
    try {
      await this.page.waitForSelector(selector, { timeout: 5000 });
      this.logResult(testName, true, '元素存在');
      return true;
    } catch (error) {
      this.logResult(testName, false, '元素不存在');
      return false;
    }
  }

  // 测试1: 页面加载
  async testPageLoad() {
    console.log('\n📱 测试1: 页面加载');
    
    await this.page.goto(this.baseUrl);
    
    // 检查页面标题
    const title = await this.page.title();
    this.logResult('页面标题检查', title.length > 0, `标题: ${title}`);
    
    // 检查登录页面元素
    await this.checkElement('.login-container', '登录容器加载');
    await this.checkElement('input[placeholder*="用户名"]', '用户名输入框');
    await this.checkElement('input[placeholder*="密码"]', '密码输入框');
    await this.checkElement('button:has-text("登录")', '登录按钮');
    await this.checkElement('.theme-toggle', '主题切换按钮');
  }

  // 测试2: 主题切换
  async testThemeToggle() {
    console.log('\n🎨 测试2: 主题切换');
    
    // 点击主题切换按钮
    await this.clickElement('.theme-toggle', '点击主题切换按钮');
    
    // 等待主题变化
    await this.page.waitForTimeout(1000);
    
    // 检查主题是否变化（通过CSS变量检查）
    const themeColor = await this.page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue('--theme-primary');
    });
    
    this.logResult('主题切换检查', themeColor.length > 0, `主题色: ${themeColor}`);
  }

  // 测试3: 用户登录
  async testLogin() {
    console.log('\n🔐 测试3: 用户登录');
    
    // 输入用户名
    await this.typeText('input[placeholder*="用户名"]', 'testuser', '输入用户名');
    
    // 输入密码
    await this.typeText('input[placeholder*="密码"]', '123456', '输入密码');
    
    // 点击登录按钮
    await this.clickElement('button:has-text("登录")', '点击登录按钮');
    
    // 等待登录成功并跳转
    await this.waitForNavigation('**//', '登录后页面跳转');
    
    // 检查是否跳转到主页
    const currentUrl = this.page.url();
    const isHomePage = currentUrl.includes('localhost:5174') && !currentUrl.includes('/login');
    this.logResult('登录跳转检查', isHomePage, `当前URL: ${currentUrl}`);
  }

  // 测试4: 主页功能
  async testHomePage() {
    console.log('\n🏠 测试4: 主页功能');
    
    // 检查主页元素
    await this.checkElement('.van-tabbar', '底部导航栏');
    await this.checkElement('[name="chat"]', '聊天标签');
    await this.checkElement('[name="contacts"]', '联系人标签');
    await this.checkElement('[name="profile"]', '我的标签');
    
    // 检查聊天列表
    await this.checkElement('.chat-list', '聊天列表容器');
  }

  // 测试5: 联系人页面
  async testContactsPage() {
    console.log('\n👥 测试5: 联系人页面');
    
    // 点击联系人标签
    await this.clickElement('[name="contacts"]', '点击联系人标签');
    
    // 等待页面加载
    await this.page.waitForTimeout(1000);
    
    // 检查联系人页面元素
    await this.checkElement('.contacts-container', '联系人页面容器');
    await this.checkElement('text=新的朋友', '新的朋友入口');
    
    // 检查好友列表
    const friendsExist = await this.checkElement('.friend-item', '好友列表项');
    
    if (friendsExist) {
      // 点击第一个好友开始聊天
      await this.clickElement('.friend-item:first-child', '点击好友开始聊天');
      
      // 检查是否跳转到聊天页面
      await this.page.waitForTimeout(2000);
      const currentUrl = this.page.url();
      const isChatPage = currentUrl.includes('/chat/');
      this.logResult('好友聊天跳转', isChatPage, `聊天页面URL: ${currentUrl}`);
    }
  }

  // 测试6: 聊天功能
  async testChatFunction() {
    console.log('\n💬 测试6: 聊天功能');
    
    // 如果不在聊天页面，先导航到聊天页面
    const currentUrl = this.page.url();
    if (!currentUrl.includes('/chat/')) {
      // 回到联系人页面，点击好友
      await this.clickElement('[name="contacts"]', '返回联系人页面');
      await this.page.waitForTimeout(1000);
      
      const friendExists = await this.checkElement('.friend-item', '检查好友是否存在');
      if (friendExists) {
        await this.clickElement('.friend-item:first-child', '点击好友进入聊天');
        await this.page.waitForTimeout(2000);
      } else {
        this.logResult('聊天功能测试', false, '没有好友可以聊天');
        return;
      }
    }
    
    // 检查聊天页面元素
    await this.checkElement('.chat-container', '聊天页面容器');
    await this.checkElement('.message-list', '消息列表');
    await this.checkElement('.input-area', '输入区域');
    
    // 测试发送消息
    const messageInput = 'textarea[placeholder*="输入消息"]';
    const sendButton = 'button:has-text("发送")';
    
    if (await this.checkElement(messageInput, '消息输入框')) {
      await this.typeText(messageInput, '这是一条测试消息 🎉', '输入测试消息');
      await this.clickElement(sendButton, '点击发送按钮');
      
      // 等待消息发送
      await this.page.waitForTimeout(2000);
      
      // 检查消息是否出现在聊天列表中
      const messageExists = await this.checkElement('.message-item', '检查消息是否发送成功');
      this.logResult('消息发送功能', messageExists, '消息发送测试');
    }
    
    // 测试Emoji功能
    const emojiButton = '.emoji-button';
    if (await this.checkElement(emojiButton, 'Emoji按钮')) {
      await this.clickElement(emojiButton, '点击Emoji按钮');
      
      // 检查Emoji选择器是否出现
      await this.checkElement('.emoji-picker', 'Emoji选择器显示');
      
      // 点击一个表情
      const emojiExists = await this.checkElement('.emoji-item', 'Emoji表情项');
      if (emojiExists) {
        await this.clickElement('.emoji-item:first-child', '选择表情');
        
        // 发送表情消息
        await this.clickElement(sendButton, '发送表情消息');
        await this.page.waitForTimeout(1000);
      }
    }
  }

  // 测试7: 退出登录
  async testLogout() {
    console.log('\n🚪 测试7: 退出登录');
    
    // 点击"我的"标签
    await this.clickElement('[name="profile"]', '点击我的标签');
    await this.page.waitForTimeout(1000);
    
    // 点击退出登录按钮
    await this.clickElement('button:has-text("退出登录")', '点击退出登录按钮');
    
    // 确认退出
    await this.clickElement('button:has-text("确认")', '确认退出登录');
    
    // 等待跳转到登录页面
    await this.waitForNavigation('**/login', '退出后跳转到登录页');
    
    // 检查是否回到登录页面
    await this.checkElement('.login-container', '确认回到登录页面');
  }

  // 测试8: 用户注册
  async testRegistration() {
    console.log('\n📝 测试8: 用户注册');
    
    // 点击注册按钮
    await this.clickElement('button:has-text("注册新账号")', '点击注册按钮');
    
    // 等待跳转到注册页面
    await this.waitForNavigation('**/register', '跳转到注册页面');
    
    // 检查注册页面元素
    await this.checkElement('.register-container', '注册页面容器');
    
    // 填写注册信息
    const timestamp = Date.now();
    await this.typeText('input[name="username"]', `testuser_${timestamp}`, '输入注册用户名');
    await this.typeText('input[name="email"]', `test_${timestamp}@example.com`, '输入邮箱');
    await this.typeText('input[name="displayName"]', `测试用户_${timestamp}`, '输入昵称');
    await this.typeText('input[name="password"]', '123456', '输入密码');
    await this.typeText('input[name="confirmPassword"]', '123456', '确认密码');
    
    // 点击注册按钮
    await this.clickElement('button:has-text("注册")', '点击注册按钮');
    
    // 等待注册成功并跳转
    await this.page.waitForTimeout(3000);
    
    // 检查是否跳转到主页
    const currentUrl = this.page.url();
    const isHomePage = !currentUrl.includes('/register') && !currentUrl.includes('/login');
    this.logResult('注册跳转检查', isHomePage, `注册后URL: ${currentUrl}`);
  }

  // 运行所有测试
  async runAllTests() {
    try {
      await this.init();
      
      await this.testPageLoad();
      await this.testThemeToggle();
      await this.testLogin();
      await this.testHomePage();
      await this.testContactsPage();
      await this.testChatFunction();
      await this.testLogout();
      await this.testRegistration();
      
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  // 生成测试报告
  generateReport() {
    console.log('\n📊 测试报告');
    console.log('='.repeat(50));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const successRate = ((passedTests / totalTests) * 100).toFixed(2);
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${successRate}%`);
    
    console.log('\n详细结果:');
    this.testResults.forEach(result => {
      const icon = result.success ? '✅' : '❌';
      console.log(`${icon} ${result.test}: ${result.message}`);
    });
    
    // 保存报告到文件
    const reportData = {
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: failedTests,
        successRate: successRate + '%'
      },
      details: this.testResults,
      timestamp: new Date().toISOString()
    };
    
    require('fs').writeFileSync('test-report.json', JSON.stringify(reportData, null, 2));
    console.log('\n📄 详细报告已保存到 test-report.json');
  }
}

// 运行测试
if (require.main === module) {
  const tester = new ChatAppTester();
  tester.runAllTests().catch(console.error);
}

module.exports = ChatAppTester;
