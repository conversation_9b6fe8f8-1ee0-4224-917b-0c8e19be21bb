-- 性能诊断脚本
-- 用于分析当前SQL执行缓慢的原因

-- 1. 检查表的大小和统计信息
SELECT 
    'AppNotifications' as table_name,
    COUNT(*) as total_rows
FROM "AppNotifications"
UNION ALL
SELECT 
    'Filling_ReportTaskAreaOrganizationUnits' as table_name,
    COUNT(*) as total_rows
FROM "Filling_ReportTaskAreaOrganizationUnits";

-- 2. 检查目标数据的分布
SELECT 
    'Total notifications' as category,
    COUNT(*) as count
FROM "AppNotifications"
UNION ALL
SELECT 
    'Report notifications' as category,
    COUNT(*) as count
FROM "AppNotifications" 
WHERE "NotificationName" LIKE '%Report%'
UNION ALL
SELECT 
    'With ReportTaskId' as category,
    COUNT(*) as count
FROM "AppNotifications" 
WHERE "NotificationName" LIKE '%Report%'
    AND json_extract("ExtraProperties", '$.ReportTaskId') IS NOT NULL
UNION ALL
SELECT 
    'With AreaOrganizationUnitId' as category,
    COUNT(*) as count
FROM "AppNotifications" 
WHERE "NotificationName" LIKE '%Report%'
    AND json_extract("ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL
UNION ALL
SELECT 
    'Missing ReportTaskAreaOrganizationUnitId' as category,
    COUNT(*) as count
FROM "AppNotifications" 
WHERE "NotificationName" LIKE '%Report%'
    AND json_extract("ExtraProperties", '$.ReportTaskId') IS NOT NULL
    AND json_extract("ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL
    AND json_extract("ExtraProperties", '$.ReportTaskAreaOrganizationUnitId') IS NULL;

-- 3. 检查现有索引
SELECT 
    name as index_name,
    tbl_name as table_name,
    sql as index_definition
FROM sqlite_master 
WHERE type = 'index' 
    AND (tbl_name = 'AppNotifications' OR tbl_name = 'Filling_ReportTaskAreaOrganizationUnits')
    AND name NOT LIKE 'sqlite_%'
ORDER BY tbl_name, name;

-- 4. 分析查询计划（检查是否使用索引）
EXPLAIN QUERY PLAN
SELECT an."NotificationId"
FROM "AppNotifications" an
WHERE an."NotificationName" LIKE '%Report%'
    AND json_extract(an."ExtraProperties", '$.ReportTaskId') IS NOT NULL
    AND json_extract(an."ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL
    AND json_extract(an."ExtraProperties", '$.ReportTaskAreaOrganizationUnitId') IS NULL
LIMIT 1000;

-- 5. 检查JOIN操作的性能
EXPLAIN QUERY PLAN
SELECT COUNT(*)
FROM "AppNotifications" an
INNER JOIN "Filling_ReportTaskAreaOrganizationUnits" rtaou 
    ON CAST(rtaou."ReportTaskId" AS TEXT) = json_extract(an."ExtraProperties", '$.ReportTaskId')
    AND CAST(rtaou."AreaOrganizationUnitId" AS TEXT) = json_extract(an."ExtraProperties", '$.AreaOrganizationUnitId')
WHERE an."NotificationName" LIKE '%Report%'
    AND json_extract(an."ExtraProperties", '$.ReportTaskId') IS NOT NULL
    AND json_extract(an."ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL
LIMIT 100;

-- 6. 检查数据类型一致性
SELECT 
    'ReportTaskId types' as check_type,
    typeof(json_extract("ExtraProperties", '$.ReportTaskId')) as notification_type,
    COUNT(*) as count
FROM "AppNotifications" 
WHERE json_extract("ExtraProperties", '$.ReportTaskId') IS NOT NULL
GROUP BY typeof(json_extract("ExtraProperties", '$.ReportTaskId'))
UNION ALL
SELECT 
    'AreaOrganizationUnitId types' as check_type,
    typeof(json_extract("ExtraProperties", '$.AreaOrganizationUnitId')) as notification_type,
    COUNT(*) as count
FROM "AppNotifications" 
WHERE json_extract("ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL
GROUP BY typeof(json_extract("ExtraProperties", '$.AreaOrganizationUnitId'));

-- 7. 采样检查数据格式
SELECT 
    json_extract("ExtraProperties", '$.ReportTaskId') as sample_report_task_id,
    json_extract("ExtraProperties", '$.AreaOrganizationUnitId') as sample_area_org_unit_id,
    "ExtraProperties"
FROM "AppNotifications" 
WHERE "NotificationName" LIKE '%Report%'
    AND json_extract("ExtraProperties", '$.ReportTaskId') IS NOT NULL
    AND json_extract("ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL
LIMIT 5;

-- 8. 检查目标表的数据类型
SELECT 
    "ReportTaskId",
    "AreaOrganizationUnitId",
    typeof("ReportTaskId") as report_task_id_type,
    typeof("AreaOrganizationUnitId") as area_org_unit_id_type
FROM "Filling_ReportTaskAreaOrganizationUnits"
LIMIT 5;
