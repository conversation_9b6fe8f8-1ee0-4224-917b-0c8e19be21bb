<template>
  <div class="profile-container">
    <van-nav-bar title="我的" fixed placeholder />

    <div class="profile-content">
      <!-- 用户信息卡片 -->
      <div class="user-card">
        <div class="user-info">
          <van-image
            :src="user?.avatar || defaultAvatar"
            round
            width="60"
            height="60"
            fit="cover"
            class="user-avatar"
          />
          <div class="user-details">
            <h3 class="user-name">{{ user?.displayName || user?.username }}</h3>
            <p class="user-username">用户名: {{ user?.username }}</p>
            <p class="user-email">邮箱: {{ user?.email }}</p>
          </div>
        </div>
        <van-icon name="arrow" class="arrow-icon" />
      </div>

      <!-- 功能菜单 -->
      <van-cell-group>
        <van-cell title="账号设置" icon="setting-o" is-link @click="goToSettings" />
        <van-cell title="隐私设置" icon="shield-o" is-link @click="goToPrivacy" />
        <van-cell title="通知设置" icon="bell" is-link @click="goToNotifications" />
        <van-cell title="帮助与反馈" icon="question-o" is-link @click="goToHelp" />
        <van-cell title="关于我们" icon="info-o" is-link @click="goToAbout" />
      </van-cell-group>

      <!-- 退出登录 -->
      <div class="logout-section">
        <van-button
          type="danger"
          block
          round
          @click="showLogoutConfirm = true"
        >
          退出登录
        </van-button>
      </div>
    </div>

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab" fixed placeholder>
      <van-tabbar-item icon="chat-o" name="chat" @click="goToHome">聊天</van-tabbar-item>
      <van-tabbar-item icon="friends-o" name="contacts" @click="goToContacts">联系人</van-tabbar-item>
      <van-tabbar-item icon="setting-o" name="profile">我的</van-tabbar-item>
    </van-tabbar>

    <!-- 退出确认对话框 -->
    <van-dialog
      v-model:show="showLogoutConfirm"
      title="确认退出"
      message="确定要退出登录吗？"
      show-cancel-button
      @confirm="handleLogout"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'
import { useFriendsStore } from '@/stores/friends'

const router = useRouter()
const authStore = useAuthStore()
const chatStore = useChatStore()
const friendsStore = useFriendsStore()

const activeTab = ref('profile')
const showLogoutConfirm = ref(false)
const defaultAvatar = 'https://img.yzcdn.cn/vant/cat.jpeg'

const user = computed(() => authStore.user)

// 退出登录
const handleLogout = async () => {
  try {
    // 清除所有store数据
    await authStore.logout()
    chatStore.clearAll()
    friendsStore.clearAll()

    showToast.success('已退出登录')

    // 等待状态更新后再跳转
    await nextTick()
    await router.push('/login')
  } catch (error) {
    console.error('退出登录失败:', error)
    showToast.fail('退出失败')
  }
}

// 导航
const goToHome = () => {
  router.push('/')
}

const goToContacts = () => {
  router.push('/contacts')
}

const goToSettings = () => {
  // TODO: 实现账号设置页面
  showToast('功能开发中')
}

const goToPrivacy = () => {
  // TODO: 实现隐私设置页面
  showToast('功能开发中')
}

const goToNotifications = () => {
  // TODO: 实现通知设置页面
  showToast('功能开发中')
}

const goToHelp = () => {
  // TODO: 实现帮助页面
  showToast('功能开发中')
}

const goToAbout = () => {
  // TODO: 实现关于页面
  showToast('功能开发中')
}
</script>

<style scoped>
.profile-container {
  height: 100vh;
  background-color: #f7f8fa;
}

.profile-content {
  padding: 16px;
  padding-bottom: 80px;
}

.user-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  margin-right: 16px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 4px 0;
  color: #323233;
}

.user-username,
.user-email {
  font-size: 14px;
  color: #969799;
  margin: 2px 0;
}

.arrow-icon {
  color: #c8c9cc;
}

.logout-section {
  margin-top: 32px;
  padding: 0 16px;
}

@media (max-width: 480px) {
  .profile-content {
    padding: 12px;
    padding-bottom: 80px;
  }
  
  .user-card {
    padding: 16px;
  }
  
  .user-name {
    font-size: 16px;
  }
  
  .logout-section {
    padding: 0 8px;
  }
}
</style>
