-- 性能优化分析和建议
-- 当前问题：处理2000万条记录，目标160万条，每批次20万条

-- 问题1：缺少关键索引
-- 建议添加以下索引来加速查询：

-- 1. 为NotificationName添加索引（如果还没有）
CREATE INDEX IF NOT EXISTS idx_appnotifications_name 
ON "AppNotifications"("NotificationName");

-- 2. 为ExtraProperties中的JSON字段添加函数索引
-- 注意：SQLite的JSON函数语法与PostgreSQL不同
CREATE INDEX IF NOT EXISTS idx_appnotifications_report_task_id 
ON "AppNotifications"(json_extract("ExtraProperties", '$.ReportTaskId'))
WHERE json_extract("ExtraProperties", '$.ReportTaskId') IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_appnotifications_area_org_unit_id 
ON "AppNotifications"(json_extract("ExtraProperties", '$.AreaOrganizationUnitId'))
WHERE json_extract("ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL;

-- 3. 为目标表添加复合索引
CREATE INDEX IF NOT EXISTS idx_report_task_area_lookup 
ON "Filling_ReportTaskAreaOrganizationUnits"("ReportTaskId", "AreaOrganizationUnitId");

-- 问题2：SQL语法不兼容
-- 当前脚本使用PostgreSQL语法，但项目使用SQLite
-- 需要转换为SQLite兼容的语法

-- 问题3：批处理逻辑可以优化
-- 建议使用更高效的批处理方式

-- SQLite兼容的优化版本：
-- 第一步：创建索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_notifications_composite 
ON "AppNotifications"(
    "NotificationName",
    json_extract("ExtraProperties", '$.ReportTaskId'),
    json_extract("ExtraProperties", '$.AreaOrganizationUnitId')
)
WHERE "NotificationName" LIKE '%Report%'
AND json_extract("ExtraProperties", '$.ReportTaskId') IS NOT NULL
AND json_extract("ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL;

-- 第二步：使用更高效的批处理策略
-- 避免在循环中使用EXECUTE，直接使用JOIN

-- 优化后的批处理脚本（SQLite版本）：
WITH target_notifications AS (
    SELECT 
        an."NotificationId",
        json_extract(an."ExtraProperties", '$.ReportTaskId') AS report_task_id,
        json_extract(an."ExtraProperties", '$.AreaOrganizationUnitId') AS area_org_unit_id
    FROM "AppNotifications" an
    WHERE an."NotificationName" LIKE '%Report%'
        AND json_extract(an."ExtraProperties", '$.ReportTaskId') IS NOT NULL
        AND json_extract(an."ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL
        AND json_extract(an."ExtraProperties", '$.ReportTaskAreaOrganizationUnitId') IS NULL
    LIMIT 20000  -- 减少批次大小以提高响应速度
),
matched_data AS (
    SELECT 
        tn."NotificationId",
        rtaou."Id" AS report_task_area_org_unit_id
    FROM target_notifications tn
    INNER JOIN "Filling_ReportTaskAreaOrganizationUnits" rtaou 
        ON rtaou."ReportTaskId" = tn.report_task_id
        AND rtaou."AreaOrganizationUnitId" = tn.area_org_unit_id
)
UPDATE "AppNotifications" 
SET "ExtraProperties" = json_set(
    "ExtraProperties", 
    '$.ReportTaskAreaOrganizationUnitId', 
    (SELECT md.report_task_area_org_unit_id 
     FROM matched_data md 
     WHERE md."NotificationId" = "AppNotifications"."NotificationId")
)
WHERE "NotificationId" IN (SELECT "NotificationId" FROM matched_data);

-- 性能监控查询
SELECT 
    COUNT(*) as total_to_process
FROM "AppNotifications" 
WHERE "NotificationName" LIKE '%Report%'
    AND json_extract("ExtraProperties", '$.ReportTaskId') IS NOT NULL
    AND json_extract("ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL
    AND json_extract("ExtraProperties", '$.ReportTaskAreaOrganizationUnitId') IS NULL;

-- 检查索引使用情况
EXPLAIN QUERY PLAN
SELECT an."NotificationId"
FROM "AppNotifications" an
WHERE an."NotificationName" LIKE '%Report%'
    AND json_extract(an."ExtraProperties", '$.ReportTaskId') IS NOT NULL
    AND json_extract(an."ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL
LIMIT 1000;
