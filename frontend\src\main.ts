import './assets/main.css'
import './styles/mobile.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// Vant UI组件库
import Vant from 'vant'
import 'vant/lib/index.css'

// 移动端触摸模拟器
import '@vant/touch-emulator'

// 认证store初始化
import { useAuthStore } from './stores/auth'

// 移动端适配
import { initMobileAdaptation } from './utils/mobile'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Vant)

// 初始化移动端适配
initMobileAdaptation()

// 初始化认证状态
const authStore = useAuthStore()
authStore.initialize()

app.mount('#app')
