<template>
  <div class="group-chat-container">
    <van-nav-bar
      title="群聊管理"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
    >
      <template #right>
        <van-icon name="plus" size="18" @click="showCreateGroup = true" />
      </template>
    </van-nav-bar>

    <div class="group-content">
      <!-- 群聊列表 -->
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <div v-if="mockGroups.length === 0" class="empty-state">
          <van-empty description="暂无群聊">
            <van-button type="primary" size="small" @click="showCreateGroup = true">
              创建群聊
            </van-button>
          </van-empty>
        </div>
        
        <van-cell-group v-else>
          <van-cell
            v-for="group in mockGroups"
            :key="group.id"
            :title="group.name"
            :label="`${group.memberCount}人 · ${group.lastMessage || '暂无消息'}`"
            is-link
            @click="enterGroup(group)"
          >
            <template #icon>
              <van-image
                :src="group.avatar || defaultGroupAvatar"
                round
                width="40"
                height="40"
                fit="cover"
                class="group-avatar"
              />
            </template>
            
            <template #right-icon>
              <van-icon name="ellipsis" @click.stop="showGroupMenu(group)" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-pull-refresh>
    </div>

    <!-- 创建群聊弹窗 -->
    <van-popup
      v-model:show="showCreateGroup"
      position="bottom"
      :style="{ height: '60%' }"
      round
    >
      <div class="create-group-popup">
        <div class="popup-header">
          <h3>创建群聊</h3>
          <van-icon name="cross" @click="showCreateGroup = false" />
        </div>
        
        <van-form @submit="handleCreateGroup">
          <van-field
            v-model="newGroup.name"
            label="群名称"
            placeholder="请输入群名称"
            :rules="[{ required: true, message: '请输入群名称' }]"
          />
          <van-field
            v-model="newGroup.description"
            label="群描述"
            placeholder="请输入群描述（可选）"
            type="textarea"
            rows="3"
          />
          
          <div class="form-actions">
            <van-button
              block
              type="primary"
              native-type="submit"
              :loading="creating"
            >
              创建群聊
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>

    <!-- 群聊操作菜单 -->
    <van-action-sheet
      v-model:show="showGroupActions"
      :actions="groupActions"
      @select="onGroupActionSelect"
      cancel-text="取消"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { useChatStore } from '@/stores/chat'

const router = useRouter()
const chatStore = useChatStore()

const refreshing = ref(false)
const showCreateGroup = ref(false)
const showGroupActions = ref(false)
const creating = ref(false)
const selectedGroup = ref<any>(null)

const defaultGroupAvatar = 'https://img.yzcdn.cn/vant/cat.jpeg'

// 模拟群聊数据
const mockGroups = ref([
  {
    id: 1,
    name: '前端开发交流群',
    description: '讨论前端技术',
    avatar: '',
    memberCount: 128,
    lastMessage: '张三: 大家好！',
    isOwner: true
  },
  {
    id: 2,
    name: '产品设计讨论',
    description: '产品设计相关讨论',
    avatar: '',
    memberCount: 56,
    lastMessage: '李四: 新版本UI很棒',
    isOwner: false
  }
])

const newGroup = reactive({
  name: '',
  description: ''
})

const groupActions = [
  { name: '进入群聊', icon: 'chat-o' },
  { name: '群聊信息', icon: 'info-o' },
  { name: '退出群聊', icon: 'delete-o', color: '#ee0a24' }
]

// 刷新群聊列表
const onRefresh = async () => {
  try {
    // TODO: 调用真实API获取群聊列表
    await new Promise(resolve => setTimeout(resolve, 1000))
  } catch (error) {
    console.error('刷新失败:', error)
  } finally {
    refreshing.value = false
  }
}

// 创建群聊
const handleCreateGroup = async () => {
  if (!newGroup.name.trim()) {
    showToast('请输入群名称')
    return
  }

  creating.value = true
  try {
    // TODO: 调用真实API创建群聊
    const mockNewGroup = {
      id: Date.now(),
      name: newGroup.name,
      description: newGroup.description,
      avatar: '',
      memberCount: 1,
      lastMessage: '',
      isOwner: true
    }
    
    mockGroups.value.unshift(mockNewGroup)
    
    showToast({ type: 'success', message: '群聊创建成功' })
    showCreateGroup.value = false
    
    // 重置表单
    newGroup.name = ''
    newGroup.description = ''
  } catch (error) {
    console.error('创建群聊失败:', error)
    showToast({ type: 'fail', message: '创建失败' })
  } finally {
    creating.value = false
  }
}

// 进入群聊
const enterGroup = (group: any) => {
  // 添加群聊会话到聊天store
  chatStore.addChatSession({
    id: group.id.toString(),
    type: 'group',
    name: group.name,
    avatar: group.avatar,
    unreadCount: 0
  })
  
  // 跳转到聊天页面
  router.push(`/chat/${group.id}`)
}

// 显示群聊菜单
const showGroupMenu = (group: any) => {
  selectedGroup.value = group
  showGroupActions.value = true
}

// 群聊操作选择
const onGroupActionSelect = async (action: any) => {
  showGroupActions.value = false
  
  if (!selectedGroup.value) return
  
  switch (action.name) {
    case '进入群聊':
      enterGroup(selectedGroup.value)
      break
      
    case '群聊信息':
      showToast('群聊信息功能开发中')
      break
      
    case '退出群聊':
      try {
        await showConfirmDialog({
          title: '退出群聊',
          message: `确定要退出"${selectedGroup.value.name}"吗？`
        })
        
        // TODO: 调用真实API退出群聊
        const index = mockGroups.value.findIndex(g => g.id === selectedGroup.value.id)
        if (index > -1) {
          mockGroups.value.splice(index, 1)
        }
        
        showToast({ type: 'success', message: '已退出群聊' })
      } catch (error) {
        // 用户取消
      }
      break
  }
  
  selectedGroup.value = null
}

// 返回
const goBack = () => {
  router.back()
}

onMounted(() => {
  onRefresh()
})
</script>

<style scoped>
.group-chat-container {
  height: 100vh;
  background: var(--theme-background);
}

.group-content {
  padding-top: 8px;
}

.group-avatar {
  margin-right: 12px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.create-group-popup {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--theme-border);
}

.popup-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--theme-text);
}

.form-actions {
  margin-top: 24px;
}

:deep(.van-cell-group) {
  background: var(--theme-surface);
}

:deep(.van-cell) {
  background: var(--theme-surface);
  color: var(--theme-text);
}

:deep(.van-field) {
  background: var(--theme-surface);
}

:deep(.van-field__control) {
  color: var(--theme-text);
}

:deep(.van-field__label) {
  color: var(--theme-text);
}
</style>
