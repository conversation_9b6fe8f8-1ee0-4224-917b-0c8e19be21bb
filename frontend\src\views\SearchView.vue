<template>
  <div class="search-container">
    <van-nav-bar
      title="添加好友"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
    />

    <div class="search-content">
      <!-- 搜索框 -->
      <div class="search-bar">
        <van-search
          v-model="searchQuery"
          placeholder="搜索用户名、昵称或邮箱"
          @search="handleSearch"
          @input="onSearchInput"
          show-action
        >
          <template #action>
            <div @click="handleSearch">搜索</div>
          </template>
        </van-search>
      </div>

      <!-- 搜索结果 -->
      <div class="search-results">
        <van-loading v-if="isSearching" class="loading" />
        
        <div v-else-if="searchResults.length > 0">
          <van-cell-group>
            <van-cell
              v-for="user in searchResults"
              :key="user.id"
              :title="user.displayName"
              :label="user.username"
            >
              <template #icon>
                <van-image
                  :src="user.avatar || defaultAvatar"
                  round
                  width="40"
                  height="40"
                  fit="cover"
                  class="user-avatar"
                />
              </template>
              
              <template #right-icon>
                <van-button
                  v-if="!user.friendshipStatus"
                  type="primary"
                  size="small"
                  @click="sendFriendRequest(user.id)"
                  :loading="sendingRequest === user.id"
                >
                  添加
                </van-button>
                
                <van-tag
                  v-else-if="user.friendshipStatus === 'Pending'"
                  type="warning"
                  size="medium"
                >
                  已发送
                </van-tag>
                
                <van-tag
                  v-else-if="user.friendshipStatus === 'Accepted'"
                  type="success"
                  size="medium"
                >
                  已是好友
                </van-tag>
                
                <van-tag
                  v-else-if="user.friendshipStatus === 'Blocked'"
                  type="danger"
                  size="medium"
                >
                  已拉黑
                </van-tag>
              </template>
            </van-cell>
          </van-cell-group>
        </div>
        
        <div v-else-if="searchQuery && !isSearching" class="empty-result">
          <van-empty description="未找到相关用户" />
        </div>
        
        <div v-else class="search-tips">
          <van-empty description="输入用户名、昵称或邮箱搜索用户" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useFriendsStore } from '@/stores/friends'
import { debounce } from 'lodash-es'

const router = useRouter()
const friendsStore = useFriendsStore()

const searchQuery = ref('')
const sendingRequest = ref<number | null>(null)
const defaultAvatar = 'https://img.yzcdn.cn/vant/cat.jpeg'

const searchResults = computed(() => friendsStore.searchResults)
const isSearching = computed(() => friendsStore.isSearching)

// 防抖搜索
const debouncedSearch = debounce(async (query: string) => {
  if (query.trim()) {
    try {
      await friendsStore.searchUsers(query.trim())
    } catch (error) {
      console.error('搜索失败:', error)
      showToast.fail('搜索失败')
    }
  } else {
    friendsStore.clearSearchResults()
  }
}, 500)

// 搜索输入
const onSearchInput = (value: string) => {
  debouncedSearch(value)
}

// 执行搜索
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    showToast('请输入搜索内容')
    return
  }
  
  try {
    await friendsStore.searchUsers(searchQuery.value.trim())
  } catch (error) {
    console.error('搜索失败:', error)
    showToast.fail('搜索失败')
  }
}

// 发送好友请求
const sendFriendRequest = async (userId: number) => {
  sendingRequest.value = userId
  
  try {
    await friendsStore.sendFriendRequest(userId)
    showToast({ type: 'success', message: '好友请求已发送' })
  } catch (error: any) {
    console.error('发送好友请求失败:', error)
    showToast({ type: 'fail', message: error.response?.data?.message || '发送失败' })
  } finally {
    sendingRequest.value = null
  }
}

// 返回
const goBack = () => {
  // 清除搜索结果
  friendsStore.clearSearchResults()
  router.back()
}
</script>

<style scoped>
.search-container {
  height: 100vh;
  background-color: #f7f8fa;
}

.search-content {
  padding-top: 8px;
}

.search-bar {
  background: white;
  padding: 8px 0;
}

.search-results {
  margin-top: 8px;
}

.loading {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.user-avatar {
  margin-right: 12px;
}

.empty-result,
.search-tips {
  padding: 60px 20px;
  text-align: center;
}

@media (max-width: 480px) {
  .search-content {
    padding-top: 4px;
  }
}
</style>
