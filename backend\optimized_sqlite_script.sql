-- 优化后的SQLite兼容批处理脚本
-- 解决性能问题的关键改进：

-- 1. 首先创建必要的索引
CREATE INDEX IF NOT EXISTS idx_appnotifications_name_json 
ON "AppNotifications"("NotificationName", json_extract("ExtraProperties", '$.ReportTaskId'))
WHERE "NotificationName" LIKE '%Report%';

CREATE INDEX IF NOT EXISTS idx_filling_composite 
ON "Filling_ReportTaskAreaOrganizationUnits"("ReportTaskId", "AreaOrganizationUnitId");

-- 2. 使用更小的批次大小（2000 -> 5000）提高响应速度
-- 3. 避免复杂的循环和动态SQL
-- 4. 使用直接的JOIN操作

BEGIN TRANSACTION;

-- 创建临时表存储本批次要处理的数据
CREATE TEMP TABLE batch_notifications AS
SELECT 
    an."NotificationId",
    json_extract(an."ExtraProperties", '$.ReportTaskId') AS report_task_id,
    json_extract(an."ExtraProperties", '$.AreaOrganizationUnitId') AS area_org_unit_id
FROM "AppNotifications" an
WHERE an."NotificationName" LIKE '%Report%'
    AND json_extract(an."ExtraProperties", '$.ReportTaskId') IS NOT NULL
    AND json_extract(an."ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL
    AND json_extract(an."ExtraProperties", '$.ReportTaskAreaOrganizationUnitId') IS NULL
LIMIT 5000;  -- 减少批次大小

-- 创建索引加速临时表操作
CREATE INDEX idx_batch_lookup ON batch_notifications(report_task_id, area_org_unit_id);

-- 一次性查找所有匹配的记录
CREATE TEMP TABLE matched_records AS
SELECT 
    bn."NotificationId",
    rtaou."Id" AS report_task_area_org_unit_id
FROM batch_notifications bn
INNER JOIN "Filling_ReportTaskAreaOrganizationUnits" rtaou 
    ON CAST(rtaou."ReportTaskId" AS TEXT) = CAST(bn.report_task_id AS TEXT)
    AND CAST(rtaou."AreaOrganizationUnitId" AS TEXT) = CAST(bn.area_org_unit_id AS TEXT);

-- 显示匹配统计
SELECT 
    (SELECT COUNT(*) FROM batch_notifications) AS batch_size,
    (SELECT COUNT(*) FROM matched_records) AS matches_found,
    ROUND(
        CAST((SELECT COUNT(*) FROM matched_records) AS REAL) / 
        CAST((SELECT COUNT(*) FROM batch_notifications) AS REAL) * 100, 2
    ) AS match_percentage;

-- 执行批量更新
UPDATE "AppNotifications" 
SET "ExtraProperties" = json_set(
    "ExtraProperties", 
    '$.ReportTaskAreaOrganizationUnitId', 
    (SELECT CAST(mr.report_task_area_org_unit_id AS TEXT)
     FROM matched_records mr 
     WHERE mr."NotificationId" = "AppNotifications"."NotificationId")
)
WHERE "NotificationId" IN (SELECT "NotificationId" FROM matched_records);

-- 显示更新结果
SELECT 
    'Updated ' || changes() || ' records in this batch' AS result;

-- 显示剩余工作量
SELECT 
    COUNT(*) AS remaining_records
FROM "AppNotifications" 
WHERE "NotificationName" LIKE '%Report%'
    AND json_extract("ExtraProperties", '$.ReportTaskId') IS NOT NULL
    AND json_extract("ExtraProperties", '$.AreaOrganizationUnitId') IS NOT NULL
    AND json_extract("ExtraProperties", '$.ReportTaskAreaOrganizationUnitId') IS NULL;

-- 清理临时表
DROP TABLE batch_notifications;
DROP TABLE matched_records;

COMMIT;
